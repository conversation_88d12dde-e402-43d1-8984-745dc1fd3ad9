{"extends": "next/core-web-vitals", "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "prefer-const": "off", "no-unused-vars": "off", "no-console": "off", "react/jsx-key": "off", "@typescript-eslint/no-unused-expressions": "off", "@typescript-eslint/ban-ts-comment": "off", "react-hooks/rules-of-hooks": "off"}}